// Unified Authentication System Tests
const request = require('supertest');

// Mock dependencies
jest.mock('../src/models', () => {
  const mockTransaction = {
    commit: jest.fn(),
    rollback: jest.fn()
  };

  return {
    User: {
      findByPk: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      sequelize: {
        transaction: jest.fn().mockResolvedValue(mockTransaction)
      },
      // Instance methods
      prototype: {
        isAdmin: jest.fn(),
        isSuperAdmin: jest.fn(),
        hasAdminRole: jest.fn(),
        toJSON: jest.fn()
      }
    },
    UserProfile: {
      findByPk: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      destroy: jest.fn()
    },
    School: {
      findAll: jest.fn(),
      searchOptimized: jest.fn(),
      fullTextSearch: jest.fn(),
      searchByLocation: jest.fn(),
      getLocationStats: jest.fn(),
      create: jest.fn()
    }
  };
});

jest.mock('../src/utils/jwt', () => ({
  generateToken: jest.fn(),
  verifyToken: jest.fn()
}));

jest.mock('../src/utils/password', () => ({
  hashPassword: jest.fn(),
  comparePassword: jest.fn(),
  validatePassword: jest.fn()
}));

const app = require('../src/app');
const { User, UserProfile, School } = require('../src/models');
const { generateToken, verifyToken } = require('../src/utils/jwt');
const { hashPassword, comparePassword, validatePassword } = require('../src/utils/password');

describe('Unified Authentication System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('User Registration', () => {
    it('should register regular user successfully', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      validatePassword.mockReturnValue({ isValid: true, errors: [] });
      User.findOne.mockResolvedValue(null);
      hashPassword.mockResolvedValue('hashedPassword');
      
      const mockUser = {
        id: 'user-id',
        email: userData.email,
        user_type: 'user',
        token_balance: 5,
        toJSON: jest.fn().mockReturnValue({
          id: 'user-id',
          email: userData.email,
          user_type: 'user',
          token_balance: 5
        })
      };
      
      User.create.mockResolvedValue(mockUser);
      generateToken.mockReturnValue('jwt-token');

      // Act
      const response = await request(app)
        .post('/auth/register')
        .send(userData);

      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.user_type).toBe('user');
      expect(response.body.data.token).toBe('jwt-token');
    });

    it('should register batch users successfully', async () => {
      // Arrange
      const usersData = [
        { email: '<EMAIL>', password: 'password123' },
        { email: '<EMAIL>', password: 'password123' }
      ];

      validatePassword.mockReturnValue({ isValid: true, errors: [] });
      User.findAll.mockResolvedValue([]);
      hashPassword.mockResolvedValue('hashedPassword');
      
      const mockUsers = usersData.map((userData, index) => ({
        id: `user-id-${index}`,
        email: userData.email,
        user_type: 'user',
        token_balance: 5,
        toJSON: jest.fn().mockReturnValue({
          id: `user-id-${index}`,
          email: userData.email,
          user_type: 'user',
          token_balance: 5
        })
      }));
      
      User.create.mockImplementation((data) => {
        const index = usersData.findIndex(u => u.email === data.email);
        return Promise.resolve(mockUsers[index]);
      });
      
      generateToken.mockReturnValue('jwt-token');

      // Act
      const response = await request(app)
        .post('/auth/register/batch')
        .send({ users: usersData });

      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.successful).toBe(2);
      expect(response.body.data.failed).toBe(0);
    });
  });

  describe('Admin Registration', () => {
    it('should register admin successfully by superadmin', async () => {
      // Arrange
      const adminData = {
        username: 'admin1',
        email: '<EMAIL>',
        password: 'Password123!',
        full_name: 'Admin User',
        user_type: 'admin'
      };

      const mockSuperAdmin = {
        id: 'superadmin-id',
        user_type: 'superadmin',
        isSuperAdmin: jest.fn().mockReturnValue(true)
      };

      validatePassword.mockReturnValue({ isValid: true, errors: [] });
      User.findOne.mockResolvedValue(null);
      hashPassword.mockResolvedValue('hashedPassword');
      
      const mockAdmin = {
        id: 'admin-id',
        username: adminData.username,
        email: adminData.email,
        user_type: 'admin',
        toJSON: jest.fn().mockReturnValue({
          id: 'admin-id',
          username: adminData.username,
          email: adminData.email,
          user_type: 'admin'
        })
      };
      
      User.create.mockResolvedValue(mockAdmin);
      UserProfile.create.mockResolvedValue({});
      User.findByPk.mockResolvedValue({
        ...mockAdmin,
        profile: { full_name: adminData.full_name }
      });
      generateToken.mockReturnValue('admin-jwt-token');

      // Mock authentication - first call for admin auth middleware
      verifyToken.mockReturnValue({ id: 'superadmin-id' });
      User.findByPk.mockResolvedValueOnce({
        ...mockSuperAdmin,
        is_active: true,
        isAdmin: jest.fn().mockReturnValue(true),
        hasAdminRole: jest.fn().mockReturnValue(true)
      });

      // Act
      const response = await request(app)
        .post('/admin/register')
        .set('Authorization', 'Bearer superadmin-token')
        .send(adminData);

      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.user_type).toBe('admin');
    });
  });

  describe('User Login', () => {
    it('should login regular user successfully', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockUser = {
        id: 'user-id',
        email: loginData.email,
        user_type: 'user',
        is_active: true,
        password_hash: 'hashedPassword',
        update: jest.fn(),
        toJSON: jest.fn().mockReturnValue({
          id: 'user-id',
          email: loginData.email,
          user_type: 'user'
        })
      };

      User.findOne.mockResolvedValue(mockUser);
      comparePassword.mockResolvedValue(true);
      generateToken.mockReturnValue('jwt-token');

      // Act
      const response = await request(app)
        .post('/auth/login')
        .send(loginData);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.user_type).toBe('user');
      expect(response.body.data.token).toBe('jwt-token');
    });
  });

  describe('Admin Login', () => {
    it('should login admin successfully', async () => {
      // Arrange
      const loginData = {
        username: 'admin1',
        password: 'Password123!'
      };

      const mockAdmin = {
        id: 'admin-id',
        username: loginData.username,
        email: '<EMAIL>',
        user_type: 'admin',
        is_active: true,
        password_hash: 'hashedPassword',
        update: jest.fn(),
        toJSON: jest.fn().mockReturnValue({
          id: 'admin-id',
          username: loginData.username,
          email: '<EMAIL>',
          user_type: 'admin'
        })
      };

      User.findOne.mockResolvedValue(mockAdmin);
      comparePassword.mockResolvedValue(true);
      generateToken.mockReturnValue('admin-jwt-token');

      // Act
      const response = await request(app)
        .post('/admin/login')
        .send(loginData);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.user_type).toBe('admin');
      expect(response.body.data.token).toBe('admin-jwt-token');
    });
  });

  describe('Profile Management', () => {
    it('should get user profile with profile data', async () => {
      // Arrange
      const mockUser = {
        id: 'user-id',
        email: '<EMAIL>',
        user_type: 'user',
        is_active: true
      };

      const mockUserWithProfile = {
        ...mockUser,
        profile: {
          full_name: 'John Doe',
          school_origin: 'Test University',
          date_of_birth: '1990-01-01',
          gender: 'male'
        }
      };

      verifyToken.mockReturnValue({ id: 'user-id' });
      User.findByPk.mockResolvedValueOnce(mockUser); // For auth middleware
      User.findByPk.mockResolvedValueOnce(mockUserWithProfile); // For controller

      // Act
      const response = await request(app)
        .get('/auth/profile')
        .set('Authorization', 'Bearer user-token');

      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.profile).toBeDefined();
      expect(response.body.data.user.profile.full_name).toBe('John Doe');
    });

    it('should update user profile successfully', async () => {
      // Arrange
      const updateData = {
        full_name: 'Jane Doe',
        school_origin: 'New University',
        gender: 'female'
      };

      const mockUser = {
        id: 'user-id',
        email: '<EMAIL>',
        user_type: 'user',
        is_active: true,
        update: jest.fn()
      };

      const mockProfile = {
        user_id: 'user-id',
        update: jest.fn()
      };

      const mockUpdatedUser = {
        ...mockUser,
        profile: { ...updateData, user_id: 'user-id' }
      };

      verifyToken.mockReturnValue({ id: 'user-id' });
      User.findByPk.mockResolvedValueOnce(mockUser); // For auth middleware
      User.findByPk.mockResolvedValueOnce(mockUser); // For controller check
      UserProfile.findByPk.mockResolvedValue(mockProfile);
      User.findByPk.mockResolvedValueOnce(mockUpdatedUser); // For final response

      // Act
      const response = await request(app)
        .put('/auth/profile')
        .set('Authorization', 'Bearer user-token')
        .send(updateData);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.profile.full_name).toBe('Jane Doe');
    });
  });

  afterAll(async () => {
    // Close any open handles
    await new Promise(resolve => setTimeout(resolve, 100));
  });
});
